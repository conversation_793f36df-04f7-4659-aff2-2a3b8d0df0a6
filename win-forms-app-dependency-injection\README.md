# WinForms 依赖注入演示应用程序

这是一个完整的 WinForms 应用程序，演示了如何在桌面应用程序中使用现代 .NET 开发模式，包括依赖注入、配置管理、日志记录和数据验证。

## 🚀 功能特性

### 核心功能

- **人员管理系统**：完整的 CRUD 操作（创建、读取、更新、删除）
- **数据验证**：实时输入验证和友好的错误消息
- **现代化界面**：直观易用的 WinForms 界面

### 技术特性

- **依赖注入**：使用 Microsoft.Extensions.DependencyInjection
- **配置管理**：JSON 配置文件和强类型配置绑定
- **日志记录**：Serilog 结构化日志，支持控制台和文件输出
- **数据访问**：FreeSql ORM 和 SQLite 数据库
- **数据验证**：数据注解验证和自定义验证服务
- **异步编程**：全面使用 async/await 模式

## 📁 项目结构

```
WinFormsAppDependencyInjection/
├── Configuration/
│   └── AppSettings.cs          # 配置模型
├── Services/
│   ├── IDbService.cs          # 数据库服务接口
│   ├── DbService.cs           # 数据库服务实现
│   ├── IPersonService.cs      # 人员服务接口
│   ├── PersonService.cs       # 人员服务实现
│   ├── IValidationService.cs  # 验证服务接口
│   └── ValidationService.cs   # 验证服务实现
├── Person.cs                  # 数据模型（带验证注解）
├── FormMain.cs               # 主窗体（完整CRUD界面）
├── FormMain.Designer.cs      # 窗体设计器文件
├── Program.cs                # 程序入口点（DI配置）
├── appsettings.json          # 应用配置文件
└── logs/                     # 日志文件目录
```

## 🛠️ 技术栈

- **.NET 8.0** - 目标框架
- **Windows Forms** - UI 框架
- **Microsoft.Extensions.DependencyInjection** - 依赖注入容器
- **Microsoft.Extensions.Configuration** - 配置管理
- **Serilog** - 结构化日志记录
- **FreeSql** - ORM 框架
- **SQLite** - 数据库
- **System.ComponentModel.Annotations** - 数据验证

## 🚀 运行应用程序

### 前提条件

- .NET 8.0 SDK 或更高版本
- Windows 操作系统

### 构建和运行

```bash
# 克隆或下载项目
cd WinFormsAppDependencyInjection

# 还原 NuGet 包
dotnet restore

# 构建项目
dotnet build

# 运行应用程序
dotnet run
```

## 📖 使用说明

### 主界面功能

1. **数据列表**：显示所有人员信息的数据网格
2. **添加人员**：点击"添加"按钮创建新人员
3. **编辑人员**：选择一行后点击"编辑"按钮修改信息
4. **删除人员**：选择一行后点击"删除"按钮（会有确认对话框）
5. **刷新数据**：点击"刷新"按钮重新加载数据

### 数据验证规则

- **姓名**：必填，长度 2-50 个字符
- **年龄**：必须在 0-150 之间

### 日志文件

- 日志文件保存在 `logs/` 目录下
- 按天轮转，保留最近 3 天的日志
- 包含应用程序的所有操作记录

## ⚙️ 配置

### appsettings.json

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=Demo.db;Version=3;Pooling=true;Max Pool Size=100;Min Pool Size=5;"
  },
  "Application": {
    "Name": "WinForms Dependency Injection Demo",
    "Version": "1.0.0",
    "EnableAutoSyncStructure": true
  }
}
```

### 日志配置

- **控制台输出**：开发时查看实时日志
- **文件输出**：生产环境日志持久化
- **日志级别**：可在配置文件中调整

## 🏗️ 架构设计

### 依赖注入容器配置

- **单例服务**：数据库服务、FreeSql 实例
- **作用域服务**：业务服务、验证服务
- **瞬态服务**：窗体实例

### 分层架构

1. **表示层**：WinForms 窗体
2. **业务层**：服务接口和实现
3. **数据层**：FreeSql ORM 和 SQLite

### 设计模式

- **依赖注入**：降低耦合度，提高可测试性
- **仓储模式**：数据访问抽象
- **服务定位器**：服务发现和管理

## 🧪 测试

项目包含完整的单元测试：

- PersonService 测试用例
- ValidationService 测试用例
- 使用 Moq 框架进行模拟测试

```bash
# 运行测试（如果测试项目可用）
dotnet test
```

## 📝 开发说明

### 添加新功能

1. 在 `Services/` 目录下创建新的服务接口和实现
2. 在 `Program.cs` 中注册新服务
3. 在窗体中通过构造函数注入使用

### 修改配置

1. 更新 `appsettings.json` 文件
2. 如需要，更新 `Configuration/AppSettings.cs` 模型
3. 重新构建并运行应用程序

### 查看日志

- 开发时：查看控制台输出
- 生产时：检查 `logs/` 目录下的日志文件

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！

## 📄 许可证

本项目仅用于学习和演示目的。
