namespace WinFormsAppDependencyInjection.Services;

/// <summary>
/// 数据库服务接口
/// 定义了数据库访问层的核心功能和资源管理
/// 提供FreeSql ORM实例和线程安全的数据库操作锁
/// 采用接口隔离原则，便于单元测试和依赖注入
/// </summary>
public interface IDbService
{
    /// <summary>
    /// FreeSql ORM 实例
    /// 提供对数据库的CRUD操作能力
    /// 支持多种数据库类型（SQLite、MySQL、SQL Server等）
    /// 包含连接池管理、事务处理、查询优化等功能
    /// 注意：应该作为单例使用，避免频繁创建和销毁
    /// </summary>
    IFreeSql FreeSql { get; }

    /// <summary>
    /// 数据库操作同步锁对象
    /// 用于确保多线程环境下数据库操作的线程安全
    /// 在需要原子性操作或避免并发冲突时使用
    /// 特别适用于SQLite等不支持高并发的数据库
    /// </summary>
    object DbLock { get; }
}