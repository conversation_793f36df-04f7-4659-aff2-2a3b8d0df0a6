﻿using System.ComponentModel.DataAnnotations;
using FreeSql.DataAnnotations;

namespace WinFormsAppDependencyInjection;

/// <summary>
/// 人员实体类
/// 用于表示系统中的人员信息，包含基本的个人数据
/// 该类使用数据注解进行验证，确保数据的完整性和有效性
/// 同时使用FreeSql注解来配置数据库映射关系
/// </summary>
public class Person
{
    /// <summary>
    /// 人员唯一标识符
    /// 作为数据库主键，设置为自增长字段
    /// 在新增人员时无需手动设置此值，由数据库自动生成
    /// </summary>
    [Column(IsIdentity = true, IsPrimary = true)]
    public int Id { get; set; }

    /// <summary>
    /// 人员姓名
    /// 必填字段，长度限制在2-50个字符之间
    /// 用于标识和显示人员的真实姓名
    /// 验证规则：不能为空，且长度必须在指定范围内
    /// </summary>
    [Required(ErrorMessage = "姓名不能为空")]
    [StringLength(50, MinimumLength = 2, ErrorMessage = "姓名长度必须在2-50个字符之间")]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 人员年龄
    /// 必须在0-150之间的有效范围内
    /// 用于记录人员的当前年龄信息
    /// 验证规则：数值必须在合理的年龄范围内
    /// </summary>
    [Range(0, 150, ErrorMessage = "年龄必须在0-150之间")]
    public int Age { get; set; }
}