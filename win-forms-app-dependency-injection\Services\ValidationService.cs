using System.ComponentModel.DataAnnotations;
using Microsoft.Extensions.Logging;

namespace WinFormsAppDependencyInjection.Services;

/// <summary>
/// 数据验证服务实现类
/// 基于.NET数据注解提供统一的验证功能
/// 支持复杂的验证规则组合和详细的错误信息收集
/// 集成日志记录，便于调试和监控验证过程
/// </summary>
public class ValidationService : IValidationService
{
    #region 私有字段

    /// <summary>
    /// 日志记录器
    /// 用于记录验证过程中的信息、警告和错误
    /// </summary>
    private readonly ILogger<ValidationService> _logger;

    #endregion

    #region 构造函数

    /// <summary>
    /// 验证服务构造函数
    /// 通过依赖注入获取日志服务
    /// </summary>
    /// <param name="logger">日志记录器实例</param>
    /// <exception cref="ArgumentNullException">当logger参数为null时抛出</exception>
    public ValidationService(ILogger<ValidationService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    #endregion

    #region 公共方法

    /// <summary>
    /// 验证指定对象的数据有效性
    /// 使用.NET内置的数据注解验证器进行全面验证
    /// </summary>
    /// <param name="obj">要验证的对象实例</param>
    /// <returns>包含验证结果和错误信息的ValidationResult对象</returns>
    public ValidationResult ValidateObject(object obj)
    {
        // 空值检查
        if (obj == null)
        {
            _logger.LogWarning("尝试验证空对象");
            return new ValidationResult(false, "对象不能为空");
        }

        try
        {
            // 创建验证上下文，包含要验证的对象实例
            var context = new ValidationContext(obj);

            // 用于收集所有验证错误的列表
            var results = new List<System.ComponentModel.DataAnnotations.ValidationResult>();

            // 执行验证，validateAllProperties=true表示验证所有属性
            bool isValid = Validator.TryValidateObject(obj, context, results, validateAllProperties: true);

            if (isValid)
            {
                // 验证成功，记录调试信息
                _logger.LogDebug("对象验证成功: {ObjectType}", obj.GetType().Name);
                return new ValidationResult(true);
            }
            else
            {
                // 验证失败，收集所有错误信息
                var errors = results.Select(r => r.ErrorMessage ?? "未知验证错误").ToList();

                _logger.LogWarning("对象验证失败: {ObjectType}, 错误数量: {ErrorCount}, 错误详情: {Errors}",
                    obj.GetType().Name, errors.Count, string.Join("; ", errors));

                return new ValidationResult(false, errors);
            }
        }
        catch (Exception ex)
        {
            // 验证过程中发生异常
            _logger.LogError(ex, "验证对象时发生异常: {ObjectType}", obj.GetType().Name);
            return new ValidationResult(false, $"验证过程中发生错误: {ex.Message}");
        }
    }

    /// <summary>
    /// 验证对象并在失败时抛出异常
    /// 适用于需要立即中断执行流程的验证场景
    /// </summary>
    /// <param name="obj">要验证的对象实例</param>
    /// <exception cref="ValidationException">
    /// 当验证失败时抛出，异常消息包含所有验证错误信息
    /// </exception>
    public void ValidateAndThrow(object obj)
    {
        // 调用验证方法获取结果
        var result = ValidateObject(obj);

        // 如果验证失败，抛出包含详细错误信息的异常
        if (!result.IsValid)
        {
            var errorMessage = string.Join("; ", result.Errors);
            _logger.LogError("验证失败，抛出异常: {ErrorMessage}", errorMessage);
            throw new ValidationException(errorMessage);
        }

        // 验证成功，正常返回
        _logger.LogDebug("验证通过，继续执行");
    }

    #endregion
}

/// <summary>
/// 数据验证结果类
/// 封装验证操作的结果信息，包括验证状态和错误详情
/// 提供统一的验证结果表示，便于上层调用者处理验证结果
/// </summary>
public class ValidationResult
{
    #region 公共属性

    /// <summary>
    /// 获取验证是否通过
    /// true: 验证成功，数据有效
    /// false: 验证失败，存在数据错误
    /// </summary>
    public bool IsValid { get; }

    /// <summary>
    /// 获取验证错误信息列表
    /// 当验证失败时，包含所有具体的错误描述
    /// 当验证成功时，列表为空
    /// </summary>
    public List<string> Errors { get; }

    #endregion

    #region 构造函数

    /// <summary>
    /// 创建验证结果实例（单个错误版本）
    /// </summary>
    /// <param name="isValid">验证是否通过</param>
    /// <param name="error">单个错误信息，可为null</param>
    public ValidationResult(bool isValid, string? error = null)
    {
        IsValid = isValid;
        Errors = error != null ? new List<string> { error } : new List<string>();
    }

    /// <summary>
    /// 创建验证结果实例（多个错误版本）
    /// </summary>
    /// <param name="isValid">验证是否通过</param>
    /// <param name="errors">错误信息列表，可为null</param>
    public ValidationResult(bool isValid, List<string> errors)
    {
        IsValid = isValid;
        Errors = errors ?? new List<string>();
    }

    #endregion
}
