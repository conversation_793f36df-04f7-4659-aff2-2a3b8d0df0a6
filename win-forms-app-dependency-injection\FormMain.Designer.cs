﻿using System.ComponentModel;

namespace WinFormsAppDependencyInjection;

partial class FormMain
{
    /// <summary>
    /// Required designer variable.
    /// </summary>
    private IContainer components = null;

    /// <summary>
    /// Clean up any resources being used.
    /// </summary>
    /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
    protected override void Dispose(bool disposing)
    {
        if (disposing && (components != null))
        {
            components.Dispose();
        }

        base.Dispose(disposing);
    }

    #region Windows Form Designer generated code

    /// <summary>
    /// Required method for Designer support - do not modify
    /// the contents of this method with the code editor.
    /// </summary>
    private void InitializeComponent()
    {
        dataGridView_Persons = new DataGridView();
        panel_Controls = new Panel();
        button_Refresh = new Button();
        button_Delete = new Button();
        button_Edit = new Button();
        button_Add = new Button();
        panel_Form = new Panel();
        label_Age = new Label();
        label_Name = new Label();
        textBox_Age = new TextBox();
        textBox_Name = new TextBox();
        button_Cancel = new Button();
        button_Save = new Button();
        ((System.ComponentModel.ISupportInitialize)dataGridView_Persons).BeginInit();
        panel_Controls.SuspendLayout();
        panel_Form.SuspendLayout();
        SuspendLayout();
        //
        // dataGridView_Persons
        //
        dataGridView_Persons.AllowUserToAddRows = false;
        dataGridView_Persons.AllowUserToDeleteRows = false;
        dataGridView_Persons.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
        dataGridView_Persons.Dock = DockStyle.Fill;
        dataGridView_Persons.Location = new Point(0, 0);
        dataGridView_Persons.MultiSelect = false;
        dataGridView_Persons.Name = "dataGridView_Persons";
        dataGridView_Persons.ReadOnly = true;
        dataGridView_Persons.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
        dataGridView_Persons.Size = new Size(600, 300);
        dataGridView_Persons.TabIndex = 0;
        dataGridView_Persons.SelectionChanged += dataGridView_Persons_SelectionChanged;
        //
        // panel_Controls
        //
        panel_Controls.Controls.Add(button_Refresh);
        panel_Controls.Controls.Add(button_Delete);
        panel_Controls.Controls.Add(button_Edit);
        panel_Controls.Controls.Add(button_Add);
        panel_Controls.Dock = DockStyle.Bottom;
        panel_Controls.Location = new Point(0, 300);
        panel_Controls.Name = "panel_Controls";
        panel_Controls.Size = new Size(600, 50);
        panel_Controls.TabIndex = 1;
        //
        // button_Refresh
        //
        button_Refresh.Location = new Point(330, 10);
        button_Refresh.Name = "button_Refresh";
        button_Refresh.Size = new Size(75, 30);
        button_Refresh.TabIndex = 3;
        button_Refresh.Text = "刷新";
        button_Refresh.UseVisualStyleBackColor = true;
        button_Refresh.Click += button_Refresh_Click;
        //
        // button_Delete
        //
        button_Delete.Enabled = false;
        button_Delete.Location = new Point(230, 10);
        button_Delete.Name = "button_Delete";
        button_Delete.Size = new Size(75, 30);
        button_Delete.TabIndex = 2;
        button_Delete.Text = "删除";
        button_Delete.UseVisualStyleBackColor = true;
        button_Delete.Click += button_Delete_Click;
        //
        // button_Edit
        //
        button_Edit.Enabled = false;
        button_Edit.Location = new Point(130, 10);
        button_Edit.Name = "button_Edit";
        button_Edit.Size = new Size(75, 30);
        button_Edit.TabIndex = 1;
        button_Edit.Text = "编辑";
        button_Edit.UseVisualStyleBackColor = true;
        button_Edit.Click += button_Edit_Click;
        //
        // button_Add
        //
        button_Add.Location = new Point(30, 10);
        button_Add.Name = "button_Add";
        button_Add.Size = new Size(75, 30);
        button_Add.TabIndex = 0;
        button_Add.Text = "添加";
        button_Add.UseVisualStyleBackColor = true;
        button_Add.Click += button_Add_Click;
        //
        // panel_Form
        //
        panel_Form.Controls.Add(label_Age);
        panel_Form.Controls.Add(label_Name);
        panel_Form.Controls.Add(textBox_Age);
        panel_Form.Controls.Add(textBox_Name);
        panel_Form.Controls.Add(button_Cancel);
        panel_Form.Controls.Add(button_Save);
        panel_Form.Dock = DockStyle.Bottom;
        panel_Form.Location = new Point(0, 350);
        panel_Form.Name = "panel_Form";
        panel_Form.Size = new Size(600, 100);
        panel_Form.TabIndex = 2;
        panel_Form.Visible = false;
        //
        // label_Age
        //
        label_Age.AutoSize = true;
        label_Age.Location = new Point(200, 20);
        label_Age.Name = "label_Age";
        label_Age.Size = new Size(32, 17);
        label_Age.TabIndex = 5;
        label_Age.Text = "年龄:";
        //
        // label_Name
        //
        label_Name.AutoSize = true;
        label_Name.Location = new Point(30, 20);
        label_Name.Name = "label_Name";
        label_Name.Size = new Size(32, 17);
        label_Name.TabIndex = 4;
        label_Name.Text = "姓名:";
        //
        // textBox_Age
        //
        textBox_Age.Location = new Point(240, 17);
        textBox_Age.Name = "textBox_Age";
        textBox_Age.Size = new Size(100, 23);
        textBox_Age.TabIndex = 3;
        //
        // textBox_Name
        //
        textBox_Name.Location = new Point(70, 17);
        textBox_Name.Name = "textBox_Name";
        textBox_Name.Size = new Size(100, 23);
        textBox_Name.TabIndex = 2;
        //
        // button_Cancel
        //
        button_Cancel.Location = new Point(450, 50);
        button_Cancel.Name = "button_Cancel";
        button_Cancel.Size = new Size(75, 30);
        button_Cancel.TabIndex = 1;
        button_Cancel.Text = "取消";
        button_Cancel.UseVisualStyleBackColor = true;
        button_Cancel.Click += button_Cancel_Click;
        //
        // button_Save
        //
        button_Save.Location = new Point(350, 50);
        button_Save.Name = "button_Save";
        button_Save.Size = new Size(75, 30);
        button_Save.TabIndex = 0;
        button_Save.Text = "保存";
        button_Save.UseVisualStyleBackColor = true;
        button_Save.Click += button_Save_Click;
        //
        // FormMain
        //
        AutoScaleDimensions = new SizeF(7F, 17F);
        AutoScaleMode = AutoScaleMode.Font;
        ClientSize = new Size(600, 450);
        Controls.Add(dataGridView_Persons);
        Controls.Add(panel_Controls);
        Controls.Add(panel_Form);
        MinimumSize = new Size(600, 400);
        Name = "FormMain";
        StartPosition = FormStartPosition.CenterScreen;
        Text = "人员管理系统";
        Load += FormMain_Load;
        ((System.ComponentModel.ISupportInitialize)dataGridView_Persons).EndInit();
        panel_Controls.ResumeLayout(false);
        panel_Form.ResumeLayout(false);
        panel_Form.PerformLayout();
        ResumeLayout(false);
    }

    private DataGridView dataGridView_Persons;
    private Panel panel_Controls;
    private Button button_Add;
    private Button button_Edit;
    private Button button_Delete;
    private Button button_Refresh;
    private Panel panel_Form;
    private TextBox textBox_Name;
    private TextBox textBox_Age;
    private Label label_Name;
    private Label label_Age;
    private Button button_Save;
    private Button button_Cancel;

    #endregion
}