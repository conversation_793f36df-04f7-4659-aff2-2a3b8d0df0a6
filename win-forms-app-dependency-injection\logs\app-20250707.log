[2025-07-07 14:36:06.851 +08:00 INF] 应用程序启动开始
[2025-07-07 14:36:06.924 +08:00 INF] 依赖注入容器配置完成
[2025-07-07 14:36:06.962 +08:00 INF] 正在初始化数据库服务...
[2025-07-07 14:36:07.201 +08:00 INF] 数据库服务初始化完成，连接字符串：Data Source=Demo.db;Version=3;Pooling=true;Max Pool Size=100;Min Pool Size=5;
[2025-07-07 14:36:07.278 +08:00 INF] 主窗体创建成功，开始运行应用程序
[2025-07-07 14:36:07.293 +08:00 INF] 主窗体开始加载
[2025-07-07 14:36:07.293 +08:00 INF] 开始从数据库加载人员数据
[2025-07-07 14:36:07.294 +08:00 INF] 正在查询所有人员信息
[2025-07-07 14:36:07.410 +08:00 INF] 成功查询到 0 个人员记录
[2025-07-07 14:36:07.426 +08:00 INF] 人员数据加载完成，共加载 0 条记录
[2025-07-07 14:36:07.427 +08:00 INF] 主窗体加载完成
[2025-07-07 14:36:15.502 +08:00 INF] 用户点击添加按钮，进入新增模式
[2025-07-07 14:36:25.730 +08:00 INF] 用户保存新增的人员: 姓名=张三
[2025-07-07 14:36:25.732 +08:00 INF] 正在添加人员: 姓名=张三, 年龄=18
[2025-07-07 14:36:25.759 +08:00 INF] 成功添加人员，影响行数: 1, 姓名: 张三
[2025-07-07 14:36:26.885 +08:00 INF] 开始从数据库加载人员数据
[2025-07-07 14:36:26.885 +08:00 INF] 正在查询所有人员信息
[2025-07-07 14:36:26.888 +08:00 INF] 成功查询到 1 个人员记录
[2025-07-07 14:36:26.908 +08:00 INF] 人员数据加载完成，共加载 1 条记录
[2025-07-07 14:36:27.749 +08:00 INF] 用户点击添加按钮，进入新增模式
[2025-07-07 14:36:33.758 +08:00 INF] 用户保存新增的人员: 姓名=李四
[2025-07-07 14:36:33.758 +08:00 INF] 正在添加人员: 姓名=李四, 年龄=19
[2025-07-07 14:36:33.762 +08:00 INF] 成功添加人员，影响行数: 1, 姓名: 李四
[2025-07-07 14:36:34.751 +08:00 INF] 开始从数据库加载人员数据
[2025-07-07 14:36:34.751 +08:00 INF] 正在查询所有人员信息
[2025-07-07 14:36:34.751 +08:00 INF] 成功查询到 2 个人员记录
[2025-07-07 14:36:34.762 +08:00 INF] 人员数据加载完成，共加载 2 条记录
[2025-07-07 14:36:36.493 +08:00 INF] 用户点击刷新按钮
[2025-07-07 14:36:36.493 +08:00 INF] 开始从数据库加载人员数据
[2025-07-07 14:36:36.493 +08:00 INF] 正在查询所有人员信息
[2025-07-07 14:36:36.493 +08:00 INF] 成功查询到 2 个人员记录
[2025-07-07 14:36:36.498 +08:00 INF] 人员数据加载完成，共加载 2 条记录
[2025-07-07 14:36:53.019 +08:00 INF] 正在释放数据库服务资源...
[2025-07-07 14:36:53.019 +08:00 INF] 数据库服务资源释放完成
[2025-07-07 14:36:53.020 +08:00 INF] 应用程序正常退出
