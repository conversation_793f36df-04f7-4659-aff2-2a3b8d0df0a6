﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>WinExe</OutputType>
        <TargetFramework>net8.0-windows</TargetFramework>
        <Nullable>enable</Nullable>
        <UseWindowsForms>true</UseWindowsForms>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="FreeSql" Version="3.5.209" />
      <PackageReference Include="FreeSql.Provider.Sqlite" Version="3.5.209" />
      <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.6" />
      <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.6" />
      <PackageReference Include="Serilog.Extensions.Hosting" Version="8.0.0" />
      <PackageReference Include="Serilog.Settings.Configuration" Version="8.0.4" />
      <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
      <PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
      <PackageReference Include="System.ComponentModel.Annotations" Version="5.0.0" />
    </ItemGroup>

    <ItemGroup>
      <Compile Update="FormMain.cs">
        <SubType>Form</SubType>
      </Compile>
    </ItemGroup>

    <ItemGroup>
      <None Update="appsettings.json">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
    </ItemGroup>

</Project>