using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Serilog;
using WinFormsAppDependencyInjection.Configuration;
using WinFormsAppDependencyInjection.Services;

namespace WinFormsAppDependencyInjection;

/// <summary>
/// 应用程序主入口类
/// 负责应用程序的启动、配置和依赖注入容器的初始化
/// 采用现代.NET开发模式，集成配置管理、日志记录和依赖注入
/// 为WinForms应用程序提供企业级的架构支持
/// </summary>
static class Program
{
    /// <summary>
    /// 应用程序主入口点
    /// 初始化日志系统、配置依赖注入容器、启动主窗体
    /// 采用异常处理确保应用程序的稳定性
    /// </summary>
    [STAThread]
    static void Main()
    {
        // 首先配置Serilog日志系统，确保后续操作都能被记录
        Log.Logger = new LoggerConfiguration()
            .ReadFrom.Configuration(BuildConfiguration())
            .CreateLogger();

        try
        {
            Log.Information("应用程序启动开始");

            // 初始化WinForms应用程序配置
            // 包括高DPI设置、默认字体等
            ApplicationConfiguration.Initialize();

            // 创建依赖注入服务容器
            var services = new ServiceCollection();
            ConfigureServices(services);

            // 构建服务提供者，完成所有依赖关系的解析
            var serviceProvider = services.BuildServiceProvider();

            Log.Information("依赖注入容器配置完成");

            // 从容器中获取主窗体实例（自动注入所有依赖）
            FormMain formMain = serviceProvider.GetRequiredService<FormMain>();

            Log.Information("主窗体创建成功，开始运行应用程序");

            // 启动WinForms消息循环
            Application.Run(formMain);

            // 应用程序正常退出时释放资源
            serviceProvider.Dispose();
            Log.Information("应用程序正常退出");
        }
        catch (Exception ex)
        {
            // 记录启动失败的详细信息
            Log.Fatal(ex, "应用程序启动失败");

            // 向用户显示友好的错误消息
            MessageBox.Show($"应用程序启动失败: {ex.Message}", "错误",
                MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
        finally
        {
            // 确保日志系统正确关闭，刷新所有待写入的日志
            Log.CloseAndFlush();
        }
    }

    /// <summary>
    /// 构建应用程序配置
    /// 从appsettings.json文件加载配置信息
    /// 支持配置文件的热重载功能
    /// </summary>
    /// <returns>配置对象，包含所有应用程序设置</returns>
    private static IConfiguration BuildConfiguration()
    {
        return new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory()) // 设置配置文件的基础路径
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true) // 加载主配置文件
            .Build();
    }

    /// <summary>
    /// 配置依赖注入服务容器
    /// 注册所有应用程序需要的服务和组件
    /// 采用不同的生命周期管理策略优化性能和资源使用
    /// </summary>
    /// <param name="services">服务集合，用于注册各种服务</param>
    private static void ConfigureServices(IServiceCollection services)
    {
        // 注册配置服务
        var configuration = BuildConfiguration();
        services.AddSingleton<IConfiguration>(configuration);
        services.Configure<AppSettings>(options => configuration.Bind(options));

        // 注册日志服务
        // 清除默认的日志提供程序，使用Serilog
        services.AddLogging(builder =>
        {
            builder.ClearProviders();
            builder.AddSerilog(); // 添加Serilog作为日志提供程序
        });

        // 注册验证服务（作用域生命周期）
        // 每个请求/操作创建新实例，适合有状态的验证逻辑
        services.AddScoped<IValidationService, ValidationService>();

        // 注册数据库服务（单例生命周期）
        // 整个应用程序共享同一个实例，确保连接池的有效利用
        services.AddSingleton<IDbService, DbService>();

        // 注册FreeSql实例（单例生命周期）
        // 从数据库服务中获取FreeSql实例，确保配置的一致性
        services.AddSingleton<IFreeSql>(provider =>
            provider.GetRequiredService<IDbService>().FreeSql);

        // 注册业务服务（作用域生命周期）
        // 每个业务操作创建新实例，确保数据的隔离性
        services.AddScoped<IPersonService, PersonService>();

        // 注册窗体（瞬态生命周期）
        // 每次请求都创建新实例，适合UI组件
        services.AddTransient<FormMain>();
    }
}