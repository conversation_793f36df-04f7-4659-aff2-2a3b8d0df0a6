{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\Temp\\win-forms-app-dependency-injection\\WinFormsAppDependencyInjection.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\Temp\\win-forms-app-dependency-injection\\WinFormsAppDependencyInjection.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Temp\\win-forms-app-dependency-injection\\WinFormsAppDependencyInjection.csproj", "projectName": "WinFormsAppDependencyInjection", "projectPath": "C:\\Users\\<USER>\\Desktop\\Temp\\win-forms-app-dependency-injection\\WinFormsAppDependencyInjection.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Temp\\win-forms-app-dependency-injection\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["d:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"FreeSql": {"target": "Package", "version": "[3.5.209, )"}, "FreeSql.Provider.Sqlite": {"target": "Package", "version": "[3.5.209, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.6, )"}, "Serilog.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.Settings.Configuration": {"target": "Package", "version": "[8.0.4, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[6.0.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[6.0.0, )"}, "System.ComponentModel.Annotations": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}}}