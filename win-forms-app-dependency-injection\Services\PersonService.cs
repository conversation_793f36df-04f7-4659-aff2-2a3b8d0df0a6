using System.ComponentModel.DataAnnotations;
using Microsoft.Extensions.Logging;

namespace WinFormsAppDependencyInjection.Services;

/// <summary>
/// 人员业务服务实现类
/// 实现了IPersonService接口，提供完整的人员管理功能
/// 集成数据验证、日志记录和异常处理，确保业务操作的可靠性
/// 使用FreeSql ORM进行数据访问，支持多种数据库类型
/// 采用依赖注入模式，提高代码的可测试性和可维护性
/// </summary>
public class PersonService : IPersonService
{
    #region 私有字段

    /// <summary>
    /// FreeSql ORM实例
    /// 用于执行数据库CRUD操作，提供强类型的数据访问能力
    /// </summary>
    private readonly IFreeSql _freeSql;

    /// <summary>
    /// 日志记录器
    /// 记录业务操作的详细信息，便于调试和监控
    /// </summary>
    private readonly ILogger<PersonService> _logger;

    /// <summary>
    /// 数据验证服务
    /// 用于验证人员数据的完整性和有效性
    /// </summary>
    private readonly IValidationService _validationService;

    #endregion

    #region 构造函数

    /// <summary>
    /// 人员服务构造函数
    /// 通过依赖注入获取所需的服务实例
    /// </summary>
    /// <param name="freeSql">FreeSql ORM实例，用于数据访问</param>
    /// <param name="logger">日志记录器，用于记录操作日志</param>
    /// <param name="validationService">验证服务，用于数据验证</param>
    /// <exception cref="ArgumentNullException">当任何参数为null时抛出</exception>
    public PersonService(IFreeSql freeSql, ILogger<PersonService> logger, IValidationService validationService)
    {
        _freeSql = freeSql ?? throw new ArgumentNullException(nameof(freeSql));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _validationService = validationService ?? throw new ArgumentNullException(nameof(validationService));
    }

    #endregion

    #region 公共方法

    /// <summary>
    /// 异步添加新人员到数据库
    /// 执行完整的数据验证流程，确保数据质量
    /// 记录详细的操作日志，便于审计和调试
    /// </summary>
    /// <param name="person">要添加的人员信息对象</param>
    /// <returns>受影响的数据库行数，通常为1表示成功</returns>
    /// <exception cref="ArgumentNullException">当person参数为null时抛出</exception>
    /// <exception cref="ValidationException">当人员信息验证失败时抛出</exception>
    public async Task<int> AddPersonAsync(Person person)
    {
        // 参数空值检查
        if (person == null)
        {
            _logger.LogError("尝试添加空的人员对象");
            throw new ArgumentNullException(nameof(person));
        }

        try
        {
            // 执行数据验证，如果验证失败会抛出ValidationException
            _validationService.ValidateAndThrow(person);

            _logger.LogInformation("正在添加人员: 姓名={Name}, 年龄={Age}", person.Name, person.Age);

            // 执行数据库插入操作
            var result = await _freeSql.Insert<Person>()
                .AppendData(person)
                .ExecuteAffrowsAsync();

            _logger.LogInformation("成功添加人员，影响行数: {AffectedRows}, 姓名: {Name}", result, person.Name);
            return result;
        }
        catch (ValidationException)
        {
            // 验证异常直接重新抛出，让上层处理
            throw;
        }
        catch (Exception ex)
        {
            // 记录其他异常并重新抛出
            _logger.LogError(ex, "添加人员时发生异常: 姓名={Name}, 年龄={Age}", person.Name, person.Age);
            throw;
        }
    }

    /// <summary>
    /// 根据人员ID异步查询特定人员信息
    /// 提供精确的单条记录查询，支持空结果处理
    /// </summary>
    /// <param name="id">要查询的人员唯一标识符</param>
    /// <returns>匹配的人员对象，如果未找到则返回null</returns>
    public async Task<Person?> GetPersonByIdAsync(int id)
    {
        try
        {
            _logger.LogInformation("正在查询ID为 {Id} 的人员信息", id);

            // 执行数据库查询，使用LINQ表达式进行条件筛选
            var person = await _freeSql.Select<Person>()
                .Where(p => p.Id == id)
                .FirstAsync();

            if (person != null)
            {
                _logger.LogInformation("成功找到人员: ID={Id}, 姓名={Name}, 年龄={Age}",
                    person.Id, person.Name, person.Age);
            }
            else
            {
                _logger.LogWarning("未找到ID为 {Id} 的人员记录", id);
            }

            return person;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "查询人员信息时发生异常: ID={Id}", id);
            throw;
        }
    }

    /// <summary>
    /// 异步获取系统中所有人员信息
    /// 返回完整的人员数据集合，适用于列表展示和数据导出
    /// </summary>
    /// <returns>包含所有人员信息的列表，如果没有数据则返回空列表</returns>
    public async Task<List<Person>> GetAllPersonsAsync()
    {
        try
        {
            _logger.LogInformation("正在查询所有人员信息");

            // 执行查询，获取所有人员记录
            var persons = await _freeSql.Select<Person>()
                .ToListAsync();

            _logger.LogInformation("成功查询到 {Count} 个人员记录", persons.Count);

            // 如果需要，可以在这里添加数据排序或过滤逻辑
            return persons;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "查询所有人员信息时发生异常");
            throw;
        }
    }

    /// <summary>
    /// 异步更新现有人员的信息
    /// 根据人员ID定位记录并更新所有字段
    /// 执行数据验证确保更新数据的有效性
    /// </summary>
    /// <param name="person">包含更新信息的人员对象，必须包含有效的ID</param>
    /// <returns>受影响的数据库行数，通常为1表示成功更新</returns>
    /// <exception cref="ArgumentNullException">当person参数为null时抛出</exception>
    /// <exception cref="ValidationException">当人员信息验证失败时抛出</exception>
    public async Task<int> UpdatePersonAsync(Person person)
    {
        // 参数空值检查
        if (person == null)
        {
            _logger.LogError("尝试更新空的人员对象");
            throw new ArgumentNullException(nameof(person));
        }

        try
        {
            // 执行数据验证
            _validationService.ValidateAndThrow(person);

            _logger.LogInformation("正在更新人员信息: ID={Id}, 姓名={Name}, 年龄={Age}",
                person.Id, person.Name, person.Age);

            // 执行数据库更新操作
            var result = await _freeSql.Update<Person>()
                .SetSource(person)
                .ExecuteAffrowsAsync();

            if (result > 0)
            {
                _logger.LogInformation("成功更新人员信息，影响行数: {AffectedRows}, ID: {Id}", result, person.Id);
            }
            else
            {
                _logger.LogWarning("更新人员信息未影响任何记录，可能记录不存在: ID={Id}", person.Id);
            }

            return result;
        }
        catch (ValidationException)
        {
            // 验证异常直接重新抛出
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新人员信息时发生异常: ID={Id}, 姓名={Name}", person.Id, person.Name);
            throw;
        }
    }

    /// <summary>
    /// 根据人员ID异步删除指定人员
    /// 执行物理删除操作，删除后数据无法恢复
    /// 建议在调用前进行用户确认
    /// </summary>
    /// <param name="id">要删除的人员唯一标识符</param>
    /// <returns>受影响的数据库行数，通常为1表示成功删除</returns>
    public async Task<int> DeletePersonAsync(int id)
    {
        try
        {
            _logger.LogInformation("正在删除ID为 {Id} 的人员", id);

            // 执行数据库删除操作
            var result = await _freeSql.Delete<Person>()
                .Where(p => p.Id == id)
                .ExecuteAffrowsAsync();

            if (result > 0)
            {
                _logger.LogInformation("成功删除人员，影响行数: {AffectedRows}, ID: {Id}", result, id);
            }
            else
            {
                _logger.LogWarning("删除操作未影响任何记录，可能记录不存在: ID={Id}", id);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除人员时发生异常: ID={Id}", id);
            throw;
        }
    }

    #endregion
}
