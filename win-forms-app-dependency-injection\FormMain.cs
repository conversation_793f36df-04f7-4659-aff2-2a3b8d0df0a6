﻿using Microsoft.Extensions.Logging;
using WinFormsAppDependencyInjection.Services;

namespace WinFormsAppDependencyInjection;

/// <summary>
/// 主窗体类
/// 提供完整的人员管理用户界面，包括CRUD操作
/// 集成数据验证、日志记录和异常处理
/// 采用依赖注入模式获取业务服务，提高代码的可测试性
/// 实现现代化的WinForms界面设计和用户体验
/// </summary>
public partial class FormMain : Form
{
    #region 私有字段

    /// <summary>
    /// 人员业务服务
    /// 提供人员数据的CRUD操作功能
    /// </summary>
    private readonly IPersonService _personService;

    /// <summary>
    /// 数据验证服务
    /// 用于验证用户输入的数据有效性
    /// </summary>
    private readonly IValidationService _validationService;

    /// <summary>
    /// 日志记录器
    /// 记录用户操作和系统事件，便于调试和监控
    /// </summary>
    private readonly ILogger<FormMain> _logger;

    /// <summary>
    /// 当前显示的人员列表
    /// 用于数据绑定和界面展示
    /// </summary>
    private List<Person> _persons = new();

    /// <summary>
    /// 当前正在编辑的人员对象
    /// 在编辑模式下保存原始数据的引用
    /// </summary>
    private Person? _currentPerson = null;

    /// <summary>
    /// 编辑模式标志
    /// true: 编辑现有人员, false: 添加新人员
    /// </summary>
    private bool _isEditing = false;

    #endregion

    #region 构造函数

    /// <summary>
    /// 主窗体构造函数
    /// 通过依赖注入获取所需的业务服务
    /// 初始化窗体组件和验证依赖项
    /// </summary>
    /// <param name="personService">人员业务服务，提供数据操作功能</param>
    /// <param name="validationService">数据验证服务，用于输入验证</param>
    /// <param name="logger">日志记录器，用于记录操作日志</param>
    /// <exception cref="ArgumentNullException">当任何参数为null时抛出</exception>
    public FormMain(IPersonService personService, IValidationService validationService, ILogger<FormMain> logger)
    {
        // 初始化窗体组件（由设计器生成）
        InitializeComponent();

        // 验证并保存依赖注入的服务实例
        _personService = personService ?? throw new ArgumentNullException(nameof(personService));
        _validationService = validationService ?? throw new ArgumentNullException(nameof(validationService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    #endregion

    #region 窗体事件处理

    /// <summary>
    /// 窗体加载事件处理程序
    /// 在窗体首次显示时执行初始化操作
    /// 加载人员数据并设置初始界面状态
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">事件参数</param>
    private async void FormMain_Load(object sender, EventArgs e)
    {
        _logger.LogInformation("主窗体开始加载");

        try
        {
            // 异步加载人员数据，避免阻塞UI线程
            await LoadPersonsAsync();
            _logger.LogInformation("主窗体加载完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "主窗体加载时发生异常");
            MessageBox.Show($"加载数据时发生错误: {ex.Message}", "错误",
                MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    #endregion

    #region 数据操作方法

    /// <summary>
    /// 异步加载人员数据到数据网格
    /// 从数据库获取所有人员信息并绑定到界面控件
    /// 包含错误处理和用户友好的错误提示
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task LoadPersonsAsync()
    {
        try
        {
            _logger.LogInformation("开始从数据库加载人员数据");

            // 从业务服务获取所有人员数据
            _persons = await _personService.GetAllPersonsAsync();

            // 重新绑定数据源，确保界面显示最新数据
            dataGridView_Persons.DataSource = null;
            dataGridView_Persons.DataSource = _persons;

            // 设置数据网格的列标题为中文
            if (dataGridView_Persons.Columns.Count > 0)
            {
                dataGridView_Persons.Columns["Id"].HeaderText = "ID";
                dataGridView_Persons.Columns["Name"].HeaderText = "姓名";
                dataGridView_Persons.Columns["Age"].HeaderText = "年龄";

                // 可以在这里设置列宽、对齐方式等格式
                dataGridView_Persons.Columns["Id"].Width = 80;
                dataGridView_Persons.Columns["Name"].Width = 150;
                dataGridView_Persons.Columns["Age"].Width = 100;
            }

            _logger.LogInformation("人员数据加载完成，共加载 {Count} 条记录", _persons.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载人员数据时发生异常");
            MessageBox.Show($"加载数据失败: {ex.Message}", "错误",
                MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    #endregion

    #region 界面事件处理

    /// <summary>
    /// 数据网格选择变化事件处理程序
    /// 根据是否有选中行来启用或禁用相关按钮
    /// 提供良好的用户体验和界面反馈
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">事件参数</param>
    private void dataGridView_Persons_SelectionChanged(object sender, EventArgs e)
    {
        // 检查是否有选中的行
        bool hasSelection = dataGridView_Persons.SelectedRows.Count > 0;

        // 根据选择状态启用或禁用操作按钮
        button_Edit.Enabled = hasSelection;
        button_Delete.Enabled = hasSelection;

        _logger.LogDebug("数据网格选择状态变化: 选中行数={SelectedCount}",
            dataGridView_Persons.SelectedRows.Count);
    }

    /// <summary>
    /// 添加按钮点击事件处理程序
    /// 切换到添加新人员模式，显示输入表单
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">事件参数</param>
    private void button_Add_Click(object sender, EventArgs e)
    {
        _logger.LogInformation("用户点击添加按钮，进入新增模式");

        // 设置为新增模式
        _isEditing = false;
        _currentPerson = null;

        // 清空表单并显示
        ClearForm();
        ShowForm();
    }

    private void button_Edit_Click(object sender, EventArgs e)
    {
        if (dataGridView_Persons.SelectedRows.Count == 0) return;

        _logger.LogInformation("用户点击编辑按钮");
        _isEditing = true;
        _currentPerson = (Person)dataGridView_Persons.SelectedRows[0].DataBoundItem;
        LoadPersonToForm(_currentPerson);
        ShowForm();
    }

    private async void button_Delete_Click(object sender, EventArgs e)
    {
        if (dataGridView_Persons.SelectedRows.Count == 0) return;

        var person = (Person)dataGridView_Persons.SelectedRows[0].DataBoundItem;

        var result = MessageBox.Show($"确定要删除人员 '{person.Name}' 吗？", "确认删除",
            MessageBoxButtons.YesNo, MessageBoxIcon.Question);

        if (result == DialogResult.Yes)
        {
            try
            {
                _logger.LogInformation("用户确认删除人员: {Name} (ID: {Id})", person.Name, person.Id);
                await _personService.DeletePersonAsync(person.Id);
                MessageBox.Show("删除成功", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                await LoadPersonsAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除人员失败: {Name} (ID: {Id})", person.Name, person.Id);
                MessageBox.Show($"删除失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }

    private async void button_Refresh_Click(object sender, EventArgs e)
    {
        _logger.LogInformation("用户点击刷新按钮");
        await LoadPersonsAsync();
    }

    /// <summary>
    /// 保存按钮点击事件处理程序
    /// 执行数据验证并保存人员信息（新增或更新）
    /// 包含完整的错误处理和用户反馈
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">事件参数</param>
    private async void button_Save_Click(object sender, EventArgs e)
    {
        try
        {
            // 从表单控件获取用户输入的数据
            var person = GetPersonFromForm();

            // 执行数据验证，确保输入的有效性
            var validationResult = _validationService.ValidateObject(person);
            if (!validationResult.IsValid)
            {
                // 显示验证错误信息给用户
                MessageBox.Show($"数据验证失败:\n{string.Join("\n", validationResult.Errors)}",
                    "验证错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // 根据当前模式执行不同的操作
            if (_isEditing && _currentPerson != null)
            {
                // 编辑模式：更新现有人员信息
                person.Id = _currentPerson.Id; // 保持原有ID
                _logger.LogInformation("用户保存编辑的人员: 姓名={Name}, ID={Id}", person.Name, person.Id);
                await _personService.UpdatePersonAsync(person);
                MessageBox.Show("更新成功", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                // 新增模式：添加新人员
                _logger.LogInformation("用户保存新增的人员: 姓名={Name}", person.Name);
                await _personService.AddPersonAsync(person);
                MessageBox.Show("添加成功", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }

            // 操作成功后隐藏表单并刷新数据
            HideForm();
            await LoadPersonsAsync();
        }
        catch (Exception ex)
        {
            // 记录异常并显示友好的错误消息
            _logger.LogError(ex, "保存人员信息时发生异常");
            MessageBox.Show($"保存失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void button_Cancel_Click(object sender, EventArgs e)
    {
        _logger.LogInformation("用户取消操作");
        HideForm();
    }

    #endregion

    #region 辅助方法

    /// <summary>
    /// 显示人员信息输入表单
    /// 使表单面板可见并设置焦点到姓名输入框
    /// </summary>
    private void ShowForm()
    {
        panel_Form.Visible = true;
        textBox_Name.Focus(); // 设置焦点，提供更好的用户体验
    }

    /// <summary>
    /// 隐藏人员信息输入表单
    /// 隐藏表单面板并清空输入内容
    /// </summary>
    private void HideForm()
    {
        panel_Form.Visible = false;
        ClearForm(); // 清空表单，为下次使用做准备
    }

    /// <summary>
    /// 清空表单中的所有输入控件
    /// 重置表单到初始状态
    /// </summary>
    private void ClearForm()
    {
        textBox_Name.Text = string.Empty;
        textBox_Age.Text = string.Empty;
    }

    /// <summary>
    /// 将人员对象的数据加载到表单控件中
    /// 用于编辑模式下的数据回显
    /// </summary>
    /// <param name="person">要加载的人员对象</param>
    private void LoadPersonToForm(Person person)
    {
        textBox_Name.Text = person.Name;
        textBox_Age.Text = person.Age.ToString();
    }

    /// <summary>
    /// 从表单控件中获取用户输入的人员信息
    /// 创建新的Person对象并填充用户输入的数据
    /// 包含基本的数据类型转换和空值处理
    /// </summary>
    /// <returns>包含用户输入数据的Person对象</returns>
    private Person GetPersonFromForm()
    {
        return new Person
        {
            Name = textBox_Name.Text.Trim(), // 去除首尾空格
            Age = int.TryParse(textBox_Age.Text.Trim(), out int age) ? age : 0 // 安全的数值转换
        };
    }

    #endregion
}