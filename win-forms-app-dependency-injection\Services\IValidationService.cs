using System.ComponentModel.DataAnnotations;

namespace WinFormsAppDependencyInjection.Services;

/// <summary>
/// 数据验证服务接口
/// 提供统一的数据验证功能，支持数据注解验证和自定义验证规则
/// 采用接口设计，便于单元测试和依赖注入
/// 支持多种验证场景：表单验证、业务规则验证、数据完整性检查等
/// </summary>
public interface IValidationService
{
    /// <summary>
    /// 验证指定对象的数据有效性
    /// 基于对象属性上的数据注解（如Required、Range、StringLength等）进行验证
    /// 返回详细的验证结果，包含所有验证错误信息
    /// </summary>
    /// <param name="obj">要验证的对象实例，不能为null</param>
    /// <returns>
    /// 验证结果对象，包含：
    /// - IsValid: 是否验证通过
    /// - Errors: 验证错误信息列表
    /// </returns>
    ValidationResult ValidateObject(object obj);

    /// <summary>
    /// 验证对象并在验证失败时抛出异常
    /// 适用于需要立即中断执行流程的场景
    /// 如果验证通过则正常返回，如果验证失败则抛出包含错误信息的异常
    /// </summary>
    /// <param name="obj">要验证的对象实例</param>
    /// <exception cref="ValidationException">
    /// 当验证失败时抛出，异常消息包含所有验证错误的详细信息
    /// </exception>
    void ValidateAndThrow(object obj);
}
