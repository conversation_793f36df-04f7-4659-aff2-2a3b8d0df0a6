namespace WinFormsAppDependencyInjection.Services;

/// <summary>
/// 人员业务服务接口
/// 定义了人员管理的核心业务操作，包括完整的CRUD功能
/// 采用异步编程模式，提高应用程序的响应性和性能
/// 遵循单一职责原则，专注于人员数据的业务逻辑处理
/// </summary>
public interface IPersonService
{
    /// <summary>
    /// 异步添加新人员到系统
    /// 执行数据验证，确保人员信息的完整性和有效性
    /// 自动生成人员ID，无需手动指定
    /// </summary>
    /// <param name="person">要添加的人员信息对象，不能为null</param>
    /// <returns>
    /// 异步任务，返回受影响的数据库行数
    /// 通常返回1表示成功添加，0表示添加失败
    /// </returns>
    /// <exception cref="ArgumentNullException">当person参数为null时抛出</exception>
    /// <exception cref="ValidationException">当人员信息验证失败时抛出</exception>
    Task<int> AddPersonAsync(Person person);

    /// <summary>
    /// 根据人员ID异步查询特定人员信息
    /// 提供精确的单条记录查询功能
    /// </summary>
    /// <param name="id">要查询的人员唯一标识符</param>
    /// <returns>
    /// 异步任务，返回匹配的人员对象
    /// 如果找到匹配记录则返回Person对象，否则返回null
    /// </returns>
    Task<Person?> GetPersonByIdAsync(int id);

    /// <summary>
    /// 异步获取系统中所有人员信息
    /// 返回完整的人员列表，适用于数据展示和报表生成
    /// 注意：如果数据量很大，建议使用分页查询
    /// </summary>
    /// <returns>
    /// 异步任务，返回包含所有人员信息的列表
    /// 如果没有人员数据则返回空列表（不会返回null）
    /// </returns>
    Task<List<Person>> GetAllPersonsAsync();

    /// <summary>
    /// 异步更新现有人员的信息
    /// 根据人员ID定位记录并更新其他字段信息
    /// 执行数据验证，确保更新数据的有效性
    /// </summary>
    /// <param name="person">包含更新信息的人员对象，必须包含有效的ID</param>
    /// <returns>
    /// 异步任务，返回受影响的数据库行数
    /// 通常返回1表示成功更新，0表示未找到匹配记录或更新失败
    /// </returns>
    /// <exception cref="ArgumentNullException">当person参数为null时抛出</exception>
    /// <exception cref="ValidationException">当人员信息验证失败时抛出</exception>
    Task<int> UpdatePersonAsync(Person person);

    /// <summary>
    /// 根据人员ID异步删除指定人员
    /// 执行物理删除操作，删除后数据无法恢复
    /// 建议在删除前进行确认操作
    /// </summary>
    /// <param name="id">要删除的人员唯一标识符</param>
    /// <returns>
    /// 异步任务，返回受影响的数据库行数
    /// 通常返回1表示成功删除，0表示未找到匹配记录
    /// </returns>
    Task<int> DeletePersonAsync(int id);
}
